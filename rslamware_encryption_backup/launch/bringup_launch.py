# Copyright (c) 2018 Intel Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os

from ament_index_python.packages import get_package_share_directory

from launch import LaunchDescription
from launch.actions import (DeclareLaunchArgument, GroupAction,
                            IncludeLaunchDescription, SetEnvironmentVariable, LogInfo)
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PythonExpression, TextSubstitution, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.actions import PushRosNamespace
from launch_ros.descriptions import ParameterFile
from nav2_common.launch import Rewritten<PERSON>aml, ReplaceString
from launch.actions import Register<PERSON>ventHandler, Shutdown
from launch.event_handlers import OnProcessExit


def generate_launch_description():
    ENCRYPTION_MODE=True

    # Get the launch directory
    nav2_bringup_dir = get_package_share_directory('rslamware_bringup')
    bringup_dir = get_package_share_directory('nav2_bringup')
    launch_dir = os.path.join(bringup_dir, 'launch')
    simulator_dir = get_package_share_directory('simulator')

    # Create the launch configuration variables
    namespace = LaunchConfiguration('namespace')
    use_namespace = LaunchConfiguration('use_namespace')
    slam = LaunchConfiguration('slam')
    map_yaml_file = LaunchConfiguration('map')
    map_pbstream_file = LaunchConfiguration('map_pbstream_file')
    mask_file = LaunchConfiguration('mask_file')
    use_sim_time = LaunchConfiguration('use_sim_time')
    params_file = LaunchConfiguration('params_file')
    autostart = LaunchConfiguration('autostart')
    use_composition = LaunchConfiguration('use_composition')
    use_respawn = LaunchConfiguration('use_respawn')
    log_level = LaunchConfiguration('log_level')
    use_als = LaunchConfiguration('use_als')
    use_cartographer_localization = LaunchConfiguration('use_cartographer_localization')
    scan_topic = LaunchConfiguration('scan_topic')

    # Map fully qualified names to relative ones so the node's namespace can be prepended.
    # In case of the transforms (tf), currently, there doesn't seem to be a better alternative
    # https://github.com/ros/geometry2/issues/32
    # https://github.com/ros/robot_state_publisher/pull/30
    # TODO(orduno) Substitute with `PushNodeRemapping`
    #              https://github.com/ros2/launch_ros/issues/56
    remappings = [('/tf', 'tf'),
                  ('/tf_static', 'tf_static')]

    # Create our own temporary YAML files that include substitutions
    param_substitutions = {
        'use_sim_time': use_sim_time,
        'yaml_filename': map_yaml_file,
        'map_pbstream_file': map_pbstream_file}

    # Only it applys when `use_namespace` is True.
    # '<robot_namespace>' keyword shall be replaced by 'namespace' launch argument
    # in config file 'nav2_multirobot_params.yaml' as a default & example.
    # User defined config file should contain '<robot_namespace>' keyword for the replacements.
    params_file = ReplaceString(
        source_file=params_file,
        replacements={'<robot_namespace>': ('/', namespace)},
        condition=IfCondition(use_namespace))

    configured_params = ParameterFile(
        RewrittenYaml(
            source_file=params_file,
            root_key=namespace,
            param_rewrites=param_substitutions,
            convert_types=True),
        allow_substs=True)

    stdout_linebuf_envvar = SetEnvironmentVariable(
        'RCUTILS_LOGGING_BUFFERED_STREAM', '1')
    

    if not ENCRYPTION_MODE:
        nav2_params = PythonExpression([
            "f'",
            TextSubstitution(text=str(simulator_dir)), "/params/turtlebot3.yaml'",
            " if '", LaunchConfiguration('mode'), "' == 'simulation' else '",
            TextSubstitution(text=str(nav2_bringup_dir)), "/config/navigation_rslamware.yaml'"
        ])
        
        mask_params = PythonExpression([
            "f'",
            TextSubstitution(text=str(get_package_share_directory('nav2_costmap_filters_demo'))), "/params/keepout_params.yaml'"
        ])
    else:
        launch_dir = os.path.dirname(os.path.realpath(__file__))
        config_dir = os.path.join(launch_dir, '..', 'config')
        config_dir = os.path.abspath(config_dir)
        nav2_params = os.path.join(config_dir, 'combined_config.yaml')
        mask_params = os.path.join(config_dir, 'combined_config.yaml')
    
    # 创建关闭监听节点
    shutdown_node = Node(
        package='shutdown_listener',
        executable='shutdown_listener',
        name='shutdown_listener',
        output='screen'
    )

    # 添加关闭处理器，当shutdown_node退出时，关闭整个launch系统
    shutdown_handler = RegisterEventHandler(
        OnProcessExit(
            target_action=shutdown_node,
            on_exit=[Shutdown()]
        )
    )
    
    declare_mode_cmd = DeclareLaunchArgument(
        name='mode',
        default_value='real',
        description='Operation mode: real | simulation | replay',
        choices=['real', 'simulation', 'replay'])

    declare_namespace_cmd = DeclareLaunchArgument(
        'namespace',
        default_value='',
        description='Top-level namespace')

    declare_use_namespace_cmd = DeclareLaunchArgument(
        'use_namespace',
        default_value='false',
        description='Whether to apply a namespace to the navigation stack')

    declare_slam_cmd = DeclareLaunchArgument(
        'slam',
        default_value='False',
        description='Whether run a SLAM')
    
    declare_map_pbstream_cmd = DeclareLaunchArgument(
        'map_pbstream_file',
        default_value='/home/<USER>/maps/map.pbstream',
        description='Enable/Disable mapping'
    )
    
    declare_map_file_cmd = DeclareLaunchArgument(
        'map',
        default_value='/home/<USER>/maps/map.yaml',
        description='Enable/Disable mapping'
    )

    declare_mask_file_cmd = DeclareLaunchArgument(
        'mask_file',
        default_value='/home/<USER>/maps/mask.yaml',
        description='Enable/Disable keepout mask filter'
    )

    declare_use_sim_time_cmd = DeclareLaunchArgument(
        'use_sim_time',
        default_value=PythonExpression(['"true" if "', LaunchConfiguration('mode'), '" == "simulation" else "false"']),
        description='Use simulation time'
    )

    declare_params_file_cmd = DeclareLaunchArgument(
        'params_file',
        default_value=nav2_params,
        description='Full path to the ROS2 parameters file to use for all launched nodes')

    declare_autostart_cmd = DeclareLaunchArgument(
        'autostart', default_value='true',
        description='Automatically startup the nav2 stack')

    declare_use_composition_cmd = DeclareLaunchArgument(
        'use_composition', default_value='True',
        description='Whether to use composed bringup')

    declare_use_respawn_cmd = DeclareLaunchArgument(
        'use_respawn', default_value='False',
        description='Whether to respawn if a node crashes. Applied when composition is disabled.')

    declare_log_level_cmd = DeclareLaunchArgument(
        'log_level', default_value='info',
        description='log level')
        
    declare_use_als_cmd = DeclareLaunchArgument(
        'use_als', default_value='False',
        description='Whether to use ALS localization')

    declare_use_cartographer_localization_cmd = DeclareLaunchArgument(
        'use_cartographer_localization',
        default_value='True',
        description='Whether to use Cartographer for localization')

    declare_scan_topic_cmd = DeclareLaunchArgument(
        'scan_topic',
        default_value='fusion_scan',
        description='Topic name for laser scan data')
    
    
    if not ENCRYPTION_MODE:
        # 启动costmap_filter_info,传入参数
        costmap_filter_info = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PathJoinSubstitution([
                    get_package_share_directory('nav2_costmap_filters_demo'),
                    'launch/costmap_filter_info.launch.py'
                ])
            ),
            launch_arguments={
                'use_sim_time': LaunchConfiguration('use_sim_time'),
                'mask': mask_file,
                'params_file': mask_params
            }.items()
        )
    else:
        # 启动costmap_filter_info,传入参数
        costmap_filter_info = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PathJoinSubstitution([
                    launch_dir,
                    'costmap_filter_info.launch.py'
                ])
            ),
            launch_arguments={
                'use_sim_time': LaunchConfiguration('use_sim_time'),
                'mask': mask_file,
                'params_file': mask_params
            }.items()
        )



    # Specify the actions
    bringup_cmd_group = GroupAction([
        PushRosNamespace(
            condition=IfCondition(use_namespace),
            namespace=namespace),

        Node(
            condition=IfCondition(use_composition),
            name='nav2_container',
            package='rclcpp_components',
            executable='component_container_isolated',
            parameters=[configured_params, {'autostart': autostart}],
            arguments=['--ros-args', '--log-level', log_level],
            remappings=remappings,
            output='screen'),

        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(os.path.join(launch_dir, 'slam_launch.py')),
            condition=IfCondition(slam),
            launch_arguments={'namespace': namespace,
                              'use_sim_time': use_sim_time,
                              'autostart': autostart,
                              'use_respawn': use_respawn,
                              'params_file': params_file}.items()),

        LogInfo(msg=PythonExpression(['"use_als: " + "', use_als, '"'])), 
        LogInfo(msg=PythonExpression(['"use_cartographer_localization: " + "', use_cartographer_localization, '"'])), 

        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PythonExpression([
                    "'", launch_dir, "/localization_als.launch.py' if ", use_als,
                    " else ('", launch_dir, "/localization_carto.launch.py' if ", use_cartographer_localization,
                    " else '", launch_dir, "/localization_launch.py')"
                ])
            ),
            condition=IfCondition(PythonExpression(['not ', slam])),
            launch_arguments={'namespace': namespace,
                              'map': map_yaml_file,
                              'map_pbstream': map_pbstream_file,
                              'use_sim_time': use_sim_time,
                              'autostart': autostart,
                              'params_file': params_file,
                              'use_composition': use_composition,
                              'use_respawn': use_respawn,
                              'container_name': 'nav2_container',
                              'scan_topic': scan_topic}.items()),

        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(os.path.join(launch_dir, 'navigation_launch.py')),
            launch_arguments={'namespace': namespace,
                              'use_sim_time': use_sim_time,
                              'autostart': autostart,
                              'params_file': params_file,
                              'use_composition': use_composition,
                              'use_respawn': use_respawn,
                              'container_name': 'nav2_container'}.items()),

        # 启动costmap_filter_info,传入参数
        costmap_filter_info,
        
        # 添加关闭监听节点
        shutdown_node,
    ])

    # Create the launch description and populate
    ld = LaunchDescription()

    # Set environment variables
    ld.add_action(stdout_linebuf_envvar)

    # Declare the launch options
    ld.add_action(declare_namespace_cmd)
    ld.add_action(declare_use_namespace_cmd)
    ld.add_action(declare_slam_cmd)
    ld.add_action(declare_use_sim_time_cmd)
    ld.add_action(declare_params_file_cmd)
    ld.add_action(declare_autostart_cmd)
    ld.add_action(declare_use_composition_cmd)
    ld.add_action(declare_use_respawn_cmd)
    ld.add_action(declare_log_level_cmd)
    ld.add_action(declare_use_als_cmd)
    ld.add_action(declare_use_cartographer_localization_cmd)
    ld.add_action(declare_map_pbstream_cmd)
    ld.add_action(declare_scan_topic_cmd)
    ld.add_action(declare_mask_file_cmd)
    ld.add_action(declare_map_file_cmd)
    ld.add_action(declare_mode_cmd)
    ld.add_action(shutdown_handler)
    ld.add_action(bringup_cmd_group)
    return ld
