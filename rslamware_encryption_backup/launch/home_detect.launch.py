#!/usr/bin/env python3

from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare


def generate_launch_description():
    pkg_share = FindPackageShare('algorithm_utils').find('algorithm_utils')

    default_params_file = PathJoinSubstitution([
        pkg_share,
        'config',
        'algorithm_params.yaml'
    ])

    params_file_arg = DeclareLaunchArgument(
        'params_file',
        default_value=default_params_file,
        description='Path to the parameters file for home detect node'
    )

    home_detect_node = Node(
        package='algorithm_utils',
        executable='home_detect_node',
        name='home_detect_node',
        parameters=[
            LaunchConfiguration('params_file')
        ],
        output='screen',
        emulate_tty=True,
    )

    return LaunchDescription([
        params_file_arg,
        home_detect_node
    ]) 