import os
from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription, DeclareLaunchArgument, LogInfo, SetEnvironmentVariable
from launch_ros.actions import Node
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution, PythonExpression, TextSubstitution  
from launch.launch_description_sources import PythonLaunchDescriptionSource
from ament_index_python.packages import get_package_share_directory
from launch.conditions import IfCondition

def generate_launch_description():

    ENCRYPTION_MODE=True

    # 启动模式，分别为真机、仿真、回放
    mode_arg = DeclareLaunchArgument(
        name='mode',
        default_value='real',
        description='Operation mode: real | simulation | replay',
        choices=['real', 'simulation', 'replay']
    )

    scan_topic_arg = DeclareLaunchArgument(
        "scan_topic",
        default_value="fusion_scan",
        description="Topic name for laser scan topic"
    )

    no_gui_arg = DeclareLaunchArgument(
        "no_gui",
        default_value="False",
        description="Flag to disable Gazebo GUI client"
    )

    bringup_dir = get_package_share_directory('rslamware_bringup')

    set_mode_env = SetEnvironmentVariable(
        name='RSLAMWARE_MODE',
        value=LaunchConfiguration('mode')
    )

    if not ENCRYPTION_MODE:
        # 控制器选择逻辑
        controller_dir = PathJoinSubstitution([bringup_dir, 'launch/controller'])
        
        real_controller = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(PathJoinSubstitution([controller_dir, 'real_robot.launch.py'])),
            condition=IfCondition(PythonExpression(['"', LaunchConfiguration('mode'), '" == "real"']))
        )
        sim_controller = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(PathJoinSubstitution([controller_dir, 'simulation.launch.py'])),
            condition=IfCondition(PythonExpression(['"', LaunchConfiguration('mode'), '" == "simulation"'])),
            launch_arguments={'no_gui': LaunchConfiguration('no_gui')}.items()
        )
        replay_controller = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(PathJoinSubstitution([controller_dir, 'replay.launch.py'])),
            condition=IfCondition(PythonExpression(['"', LaunchConfiguration('mode'), '" == "replay"']))
        )
        
        #robot_monitor only for real mode
        robot_monitor = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(PathJoinSubstitution([get_package_share_directory('robot_monitor'),
                'launch/robot_monitor.launch.py'])),
            condition=IfCondition(PythonExpression(['"', LaunchConfiguration('mode'), '" == "real"']))
        )

        robot_health = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PathJoinSubstitution([
                    get_package_share_directory('robot_health'),
                    'launch/robot_health.launch.py'
                ])
            )
        )

        agent_server = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PathJoinSubstitution([
                    get_package_share_directory('agent'),
                    'launch/agent_server.launch.py'
                ])
            ),
            launch_arguments={
                "scan_topic": LaunchConfiguration("scan_topic"),
            }.items()
        )

        # stcm service
        stcm_manager = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PathJoinSubstitution([
                    get_package_share_directory('stcm_manager'),
                    'launch/stcm_manager.launch.py'
                ])
            ),
            launch_arguments={
                'mode': LaunchConfiguration('mode'),
            }.items()
        )


    else:
        # for encrytion mode, only real mode is supported
        launch_dir = os.path.dirname(os.path.realpath(__file__))

        real_controller = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(PathJoinSubstitution([launch_dir, 'real_robot.launch.py']))
        )
        
        #robot_monitor only for real mode
        robot_monitor = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(PathJoinSubstitution([launch_dir, 'robot_monitor.launch.py']))
        )

        sim_controller = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(PathJoinSubstitution([launch_dir, 'simulation.launch.py'])),
            condition=IfCondition(PythonExpression(['"', LaunchConfiguration('mode'), '" == "simulation"'])),
            launch_arguments={'no_gui': LaunchConfiguration('no_gui')}.items()
        )
        replay_controller = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(PathJoinSubstitution([launch_dir, 'replay.launch.py'])),
            condition=IfCondition(PythonExpression(['"', LaunchConfiguration('mode'), '" == "replay"']))
        )


        robot_health = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PathJoinSubstitution([launch_dir,'robot_health.launch.py'
                ])
            )
        )

        agent_server = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PathJoinSubstitution([
                    launch_dir,
                    'agent_server.launch.py'
                ])
            ),
            launch_arguments={
                "scan_topic": LaunchConfiguration("scan_topic"),
            }.items()
        )

        # stcm service
        stcm_manager = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                PathJoinSubstitution([
                    launch_dir,
                    'stcm_manager.launch.py'
                ])
            ),
            launch_arguments={
                'mode': LaunchConfiguration('mode'),
            }.items()
        )


    rviz_config = PythonExpression([
        "f'",
        TextSubstitution(text=str(get_package_share_directory('nav2_bringup'))), "/rviz/nav2_default_view.rviz'",
        " if '", LaunchConfiguration('mode'), "' == 'simulation' else '",
        TextSubstitution(text=str(bringup_dir)), "/rviz/nav2_default_view.rviz'"
    ]) 

    return LaunchDescription([
        # 基础参数
        mode_arg,
        # 设置环境变量
        set_mode_env,
        scan_topic_arg,
        no_gui_arg,
        DeclareLaunchArgument(
            'use_sim_time',
            default_value=PythonExpression(['"true" if "', LaunchConfiguration('mode'), '" == "simulation" else "false"']),
            description='Use simulation time'
        ),

        LogInfo(msg=PythonExpression(['"Running in \'" + "', LaunchConfiguration('mode'), '" + "\' mode"'])), 

        # 控制器系统
        real_controller,
        sim_controller,
        replay_controller,

        # 监控系统
        robot_monitor,
        robot_health,

        # RESTful API
        agent_server,
        stcm_manager
    ])
