battery_monitor:
  ros__parameters:
    # CAN communication parameters
    can_interface: "can0"
    can_id_battery_info: 0x100
    can_id_battery_status: 0x101
    min_send_interval_ms: 20
    request_interval_ms: 1000

    # Input device parameters
    input_device_path: "/dev/input/event2"
    charge_event_code: 63
    manual_charge_event_code: 62
    poll_timeout_ms: 100

    # Topic and publishing parameters
    battery_topic: "battery_state"
    battery_status_topic: "battery_status"
    publish_rate_ms: 1000
    battery_status_publish_rate_ms: 20 