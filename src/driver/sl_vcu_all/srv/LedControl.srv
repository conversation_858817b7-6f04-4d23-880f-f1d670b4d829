# LED Display Control Service Definition
# Request

# Channel selection - can specify multiple channels,
# channels: IDs (0-based indexing), so far up to 2 channels are supported,
# sync_channels: If true, all channels only use the first channel_data,
#                then sends to hardware simultaneously

# ChannelData structure, refer to ChannelData.msg for details
# each channel_data has the following fields
# ##############
#    # Part selection and synchronization
#    uint8[] parts                  # Array of part IDs (0 = whole strip, 1 = first part, 2 = second part)
#    bool sync_parts                # Whether to synchronize parts within this channel

#    # Part data array
#    PartData[] parts_data          # Data for each part (size should match parts array or 1 if sync_parts=true)
# ###############


# PartData structure, refer to PartData.msg for details
# ##############
#    # Lighting mode (required)
#    string mode                    # "off", "on", "breathing", "flashing", "marquee"

#    # Color brightness settings (optional, defaults applied if not set)
#    uint8 green_brightness         # Green brightness (0-255)
#    uint8 red_brightness           # Red brightness (0-255)
#    uint8 blue_brightness          # Blue brightness (0-255)

#    # Effect parameters (optional, defaults applied if not set)
#    float32 frequency              # Frequency for breathing/flashing effects (Hz)
#    float32 speed                  # Speed for marquee effect (pixels/second)
#    bool marquee_direction         # Direction for marquee: true=forward, false=reverse
#    float32 on_time_duty           # Duty cycle for flashing effect (0.0-1.0, default 0.5)
# ##############


uint8[] channels               # Array of channel IDs (0-based indexing)
bool sync_channels             # Whether to synchronize all specified channels
ChannelData[] channel_data     # Array of channel data, each containing part data

---
# Response
bool success                   # Whether the command was executed successfully
string message                 # Success or error message
uint8[] affected_channels      # Channels that were actually affected
