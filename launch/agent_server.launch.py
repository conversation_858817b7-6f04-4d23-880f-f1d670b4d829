"""
ROS2 launch file for starting the Agent Server node
"""
from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration


def generate_launch_description():
    """Generate launch description"""
    
    # Declare launch arguments
    api_port_arg = DeclareLaunchArgument(
        'api_port',
        default_value='1448',
        description='Port for the API server'
    )
    
    api_host_arg = DeclareLaunchArgument(
        'api_host',
        default_value='0.0.0.0',
        description='Host address for the API server'
    )
    
    log_level_arg = DeclareLaunchArgument(
        'log_level',
        default_value='warn',
        description='Log level for the node (debug, info, warn, error)'
    )

    scan_topic = DeclareLaunchArgument(
        "scan_topic",
        default_value="fusion_scan",
        description="Topic name for laser scan topic"
    )
    return LaunchDescription([
        # Launch arguments
        api_port_arg,
        api_host_arg,
        log_level_arg,
        scan_topic,
        
        # Agent server node
        Node(
            package='agent',
            executable='agent_server_node.py',
            name='agent_server_node',
            output='screen',
            emulate_tty=True,
            parameters=[
                {'api_port': LaunchConfiguration('api_port')},
                {'api_host': LaunchConfiguration('api_host')},
                {'scan_topic': LaunchConfiguration('scan_topic')}
            ],
            arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
        )
    ]) 