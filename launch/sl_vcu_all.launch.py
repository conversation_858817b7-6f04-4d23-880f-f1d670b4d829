#!/usr/bin/env python3

"""
Main launch file for the SL VCU All package.
This launch file starts all the essential components:
- ZL Motor Controller
- IMU Sensor with bias correction and yaw integration
- Robot Localization EKF for sensor fusion
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction
from launch.conditions import IfCondition
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    ENCRYPTION_MODE=True

    if not ENCRYPTION_MODE:
        # Get package directory
        pkg_share = FindPackageShare('sl_vcu_all')
    else:
        launch_dir = os.path.dirname(os.path.realpath(__file__))
        pkg_share = os.path.join(launch_dir, '..')
        pkg_share = os.path.abspath(pkg_share)

    
    # Declare launch arguments
    motor_config_file_arg = DeclareLaunchArgument(
        'motor_config_file',
        default_value=PathJoinSubstitution([
            pkg_share,
            'config',
            'zl_motor_controller.yaml'
        ]),
        description='Path to the motor controller configuration file'
    )
    
    imu_config_file_arg = DeclareLaunchArgument(
        'imu_config_file',
        default_value=PathJoinSubstitution([
            pkg_share,
            'config',
            'imu_sensor.yaml'
        ]),
        description='Path to the IMU sensor configuration file'
    )

    bumper_config_file_arg = DeclareLaunchArgument(
        'bumper_config_file',
        default_value=PathJoinSubstitution([
            pkg_share,
            'config',
            'bumper_sensor.yaml'
        ]),
        description='Path to the bumper sensor configuration file'
    )

    jack_config_file_arg = DeclareLaunchArgument(
        'jack_config_file',
        default_value=PathJoinSubstitution([
            pkg_share,
            'config',
            'jack_control.yaml'
        ]),
        description='Path to the jack control configuration file'
    )

    led_config_file_arg = DeclareLaunchArgument(
        'led_config_file',
        default_value=PathJoinSubstitution([
            pkg_share,
            'config',
            'led_control.yaml'
        ]),
        description='Path to LED control configuration file'
    )

    battery_monitor_config_file_arg = DeclareLaunchArgument(
        'battery_monitor_config_file',
        default_value=PathJoinSubstitution([
            pkg_share,
            'config',
            'battery_monitor.yaml'
        ]),
        description='Path to battery monitor configuration file'
    )
    
    log_level_arg = DeclareLaunchArgument(
        'log_level',
        default_value='info',
        description='Log level for all nodes (debug, info, warn, error)'
    )
    
    odom_topic_arg = DeclareLaunchArgument(
        'odom_topic',
        default_value='odom',
        description='Odometry topic name for EKF output'
    )
    
    enable_ekf_arg = DeclareLaunchArgument(
        'enable_ekf',
        default_value='true',
        description='Enable robot localization EKF (true/false)'
    )

    imu_publish_tf_arg = DeclareLaunchArgument(
        'imu_publish_tf',
        default_value='true',
        description='Enable IMU TF publishing (true/false)'
    )

    imu_parent_frame_arg = DeclareLaunchArgument(
        'imu_parent_frame',
        default_value='base_link',
        description='Parent frame for IMU TF (e.g., base_link, odom)'
    )

    imu_child_frame_arg = DeclareLaunchArgument(
        'imu_child_frame',
        default_value='imu_link',
        description='Child frame for IMU TF (should match imu_frame_id)'
    )
    

    if not ENCRYPTION_MODE:
        # ZL Motor Controller Node
        zl_motor_controller_node = Node(
            package='sl_vcu_all',
            executable='zl_motor_controller_node',
            name='zl_motor_controller',
            parameters=[LaunchConfiguration('motor_config_file')],
            output='screen',
            arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
        )

        # IMU Sensor Node
        imu_sensor_node = Node(
            package='sl_vcu_all',
            executable='imu_sensor_node',
            name='imu_sensor',
            parameters=[
                LaunchConfiguration('imu_config_file'),
                {
                    'publish_tf': LaunchConfiguration('imu_publish_tf'),
                    'parent_frame_id': LaunchConfiguration('imu_parent_frame'),
                    'child_frame_id': LaunchConfiguration('imu_child_frame')
                }
            ],
            output='screen',
            arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
        )

        # Bumper Sensor Node
        bumper_sensor_node = Node(
            package='sl_vcu_all',
            executable='bumper_sensor_node',
            name='bumper_sensor',
            parameters=[
                LaunchConfiguration('bumper_config_file')
            ],
            output='screen',
            arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
        )

        # Jack Control Node
        jack_control_node = Node(
            package='sl_vcu_all',
            executable='jack_control_node',
            name='jack_control',
            parameters=[
                LaunchConfiguration('jack_config_file')
            ],
            output='screen',
            arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
        )

        # LED Display Control Node
        led_display_control_node = Node(
            package='sl_vcu_all',
            executable='led_display_control_node',
            name='led_display_control',
            parameters=[
                LaunchConfiguration('led_config_file')
            ],
            output='screen',
            arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
        )

        # Battery Monitor Node
        battery_monitor_node = Node(
            package='sl_vcu_all',
            executable='battery_monitor_node',
            name='battery_monitor',
            output='screen',
            parameters=[
                LaunchConfiguration('battery_monitor_config_file')
            ],
            arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
    )

    else:
        launch_dir = os.path.dirname(os.path.realpath(__file__))
        config_dir = os.path.join(launch_dir, '..', 'config')
        config_dir = os.path.abspath(config_dir)
        common_config_file = PathJoinSubstitution([
            config_dir,
            'combined_config.yaml'
        ])
        
        # ZL Motor Controller Node
        zl_motor_controller_node = Node(
            package='sl_vcu_all',
            executable='zl_motor_controller_node',
            name='zl_motor_controller',
            parameters=[common_config_file],
            output='screen',
            arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
        )

        # IMU Sensor Node
        imu_sensor_node = Node(
            package='sl_vcu_all',
            executable='imu_sensor_node',
            name='imu_sensor',
            parameters=[
                common_config_file,
                {
                    'publish_tf': LaunchConfiguration('imu_publish_tf'),
                    'parent_frame_id': LaunchConfiguration('imu_parent_frame'),
                    'child_frame_id': LaunchConfiguration('imu_child_frame')
                }
            ],
            output='screen',
            arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
        )

        # Bumper Sensor Node
        bumper_sensor_node = Node(
            package='sl_vcu_all',
            executable='bumper_sensor_node',
            name='bumper_sensor',
            parameters=[
                common_config_file
            ],
            output='screen',
            arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
        )

        # Jack Control Node
        jack_control_node = Node(
            package='sl_vcu_all',
            executable='jack_control_node',
            name='jack_control',
            parameters=[
                common_config_file
            ],
            output='screen',
            arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
        )

        # LED Display Control Node
        led_display_control_node = Node(
            package='sl_vcu_all',
            executable='led_display_control_node',
            name='led_display_control',
            parameters=[
                common_config_file
            ],
            output='screen',
            arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
        )

        # Battery Monitor Node
        battery_monitor_node = Node(
            package='sl_vcu_all',
            executable='battery_monitor_node',
            name='battery_monitor',
            output='screen',
            parameters=[
                common_config_file
            ],
            arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
        )


    return LaunchDescription([
        # Launch arguments
        motor_config_file_arg,
        imu_config_file_arg,
        bumper_config_file_arg,
        jack_config_file_arg,
        led_config_file_arg,
        battery_monitor_config_file_arg,
        # ekf_config_file_arg,
        log_level_arg,
        odom_topic_arg,
        enable_ekf_arg,
        imu_publish_tf_arg,
        imu_parent_frame_arg,
        imu_child_frame_arg,
        
        # Core nodes

        imu_sensor_node,
        zl_motor_controller_node,

        bumper_sensor_node,
        jack_control_node,
        led_display_control_node,
        battery_monitor_node,

    ])


if __name__ == '__main__':
    generate_launch_description()
