#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    # 获取包的共享目录
    pkg_share = FindPackageShare('algorithm_utils').find('algorithm_utils')
    
    # 定义默认配置文件路径
    default_params_file = PathJoinSubstitution([
        pkg_share,
        'config',
        'algorithm_params.yaml'
    ])
    
    # 声明启动参数
    params_file_arg = DeclareLaunchArgument(
        'params_file',
        default_value=default_params_file,
        description='Path to the parameters file for shelf detection node'
    )
    
    # 创建货架检测节点
    shelf_detect_node = Node(
        package='algorithm_utils',
        executable='shelf_detect_node',
        name='shelf_detect',
        parameters=[
            LaunchConfiguration('params_file')
        ],
        output='screen',
        emulate_tty=True,
    )
    
    return LaunchDescription([
        params_file_arg,
        shelf_detect_node
    ]) 