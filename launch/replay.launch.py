from launch import LaunchDescription
from launch.actions import ExecuteProcess, DeclareLaunchArgment
from launch.substitutions import LaunchConfiguration
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    rosbag = LaunchConfiguration("rosbag")
    
    rosbag_launch_arg = DeclareLaunchArgment("rosbag")
    
    shell_cmd = ExecuteProcess(
        cmd=["bash", "/opt/ros/humble/bin/data_recorder/replay.sh", rosbag],
        shell=True,
        output="screen"
    )
    
    #add other launch
    return LaunchDescription([
        rosbag_launch_arg,
        shell_cmd,
        
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
               os.path.join(get_package_share_directory('multi_lidar_data_sync'), 'launch', 'multi_lidar_data_sync.launch.py')
            )
        ),
    ])
